<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>
    <%= title %> - StreamFlow
  </title>
  <link rel="icon" href="/images/logo.svg" type="image/svg+xml">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@2.30.0/tabler-icons.min.css">
  <link rel="stylesheet" href="/css/styles.css">
  <link href="https://vjs.zencdn.net/7.20.3/video-js.css" rel="stylesheet" />
  <script src="https://vjs.zencdn.net/7.20.3/video.min.js"></script>
  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        fontFamily: {
          'inter': ['Inter', 'sans-serif'],
          'sans': ['Inter', 'system-ui', 'sans-serif']
        },
        extend: {
          colors: {
            'primary': '#0055FF',
            'secondary': '#0043CA',
            'dark': {
              '900': '#121212',
              '800': '#252525',
              '700': '#2D2D2D',
              '600': '#3D3D3D',
              '500': '#6E6E6E',
              '400': '#8F8F8F',
              '300': '#AFAFAF',
              '200': '#CFCFCF',
              '100': '#E5E5E5',
            },
            'gray': {
              '900': '#121212',
              '800': '#252525',
              '700': '#2D2D2D',
              '600': '#3D3D3D',
              '500': '#6E6E6E',
              '400': '#8F8F8F',
              '300': '#AFAFAF',
              '200': '#CFCFCF',
              '100': '#E5E5E5',
              '50': '#F5F5F5',
            }
          }
        }
      }
    }
  </script>
  <script src="/js/stream-modal.js" defer></script>
  <% if (helpers.getUserRole && helpers.getUserRole(req) === 'admin') { %>
  <script src="/socket.io/socket.io.js"></script>
  <% } %>
</head>
<body class="bg-dark-900 text-white font-inter">
  <div
    class="lg:hidden fixed top-0 left-0 right-0 h-16 bg-dark-800 shadow-lg flex items-center justify-between px-4 z-30">
    <div class="flex items-center">
      <img src="/images/logo_mobile.svg" alt="StreamFlow Logo" class="h-8">
    </div>
    <div class="flex items-center gap-1">
      <a href="https://donate.youtube101.id/" target="_blank"
         class="p-2 text-gray-400 hover:text-white transition-colors">
        <i class="ti ti-gift text-lg"></i>
      </a>
      <div class="relative">
        <button id="mobile-notification-btn" class="p-2 text-gray-400 hover:text-white transition-colors relative">
          <i class="ti ti-bell text-lg"></i>
          <span class="hidden absolute top-0.5 right-0.5 w-2 h-2 bg-red-500 rounded-full"></span>
        </button>
      </div>
    </div>
  </div>
  <div class="flex">
    <div class="hidden lg:flex lg:fixed lg:flex-col w-20 h-screen pt-2 bg-dark-800 shadow-lg overflow-hidden">
      <div class="flex items-center justify-center h-14 w-14 mx-auto mb-4">
        <img src="/images/logo.svg" alt="StreamFlow Logo" class="w-9 h-9">
      </div>
      <div class="flex-1">
        <a href="/dashboard" class="sidebar-icon group <%= active === 'dashboard' ? 'bg-primary' : '' %>">
          <i class="ti ti-broadcast text-xl"></i>
          <span class="text-xs mt-1">Streams</span>
          <span class="sidebar-tooltip group-hover:scale-100">Stream Manager</span>
        </a>
        <a href="/gallery" class="sidebar-icon group <%= active === 'gallery' ? 'bg-primary' : '' %>">
          <i class="ti ti-video text-xl"></i>
          <span class="text-xs mt-1">Gallery</span>
          <span class="sidebar-tooltip group-hover:scale-100">Video Gallery</span>
        </a>
        <a href="/history" class="sidebar-icon group <%= active === 'history' ? 'bg-primary' : '' %>">
          <i class="ti ti-history text-xl"></i>
          <span class="text-xs mt-1">History</span>
          <span class="sidebar-tooltip group-hover:scale-100">Stream History</span>
        </a>
        <a href="/subscription/plans" class="sidebar-icon group <%= active === 'subscription' ? 'bg-primary' : '' %>">
          <i class="ti ti-credit-card text-xl"></i>
          <span class="text-xs mt-1">Plans</span>
          <span class="sidebar-tooltip group-hover:scale-100">Subscription Plans</span>
        </a>
        <% if (helpers.getUserRole && helpers.getUserRole(req) === 'admin') { %>
        <a href="/admin/dashboard" class="sidebar-icon group <%= active === 'admin' ? 'bg-primary' : '' %>">
          <i class="ti ti-shield text-xl"></i>
          <span class="text-xs mt-1">Admin</span>
          <span class="sidebar-tooltip group-hover:scale-100">Admin Panel</span>
        </a>
        <% } %>
      </div>
      <div class="mt-auto">
        <div class="h-px bg-gray-700 w-full"></div>
        <div class="relative" id="profile-menu-container">
          <div class="h-20 flex items-center justify-center px-3 py-4 hover:bg-dark-700/50 transition-colors">
            <button id="profile-menu-button"
              class="w-12 h-12 rounded-full flex items-center justify-center overflow-hidden ring-2 ring-gray-700 hover:ring-primary transition-all">
              <%- helpers.getAvatar(req) %>
            </button>
          </div>
        </div>
      </div>
    </div>
    <div id="profile-dropdown" class="fixed bg-dark-800 shadow-xl border border-gray-700 hidden">
      <div class="px-4 py-2 border-b border-gray-700">
        <div class="font-medium">
          <%= helpers.getUsername(req) %>
        </div>
      </div>
      <a href="/settings"
        class="flex items-center px-4 py-2.5 text-sm text-gray-300 hover:bg-dark-700 hover:text-white transition-colors">
        <i class="ti ti-settings mr-3"></i>
        <span>Settings</span>
      </a>
      <a href="https://github.com/bangtutorial/streamflow/issues"
        class="flex items-center px-4 py-2.5 text-sm text-gray-300 hover:bg-dark-700 hover:text-white transition-colors" target="_blank">
        <i class="ti ti-help mr-3"></i>
        <span>Help & Support</span>
      </a>
      <div class="h-px bg-gray-700 mx-4"></div>
      <a href="/logout" class="flex items-center px-4 py-2.5 text-sm text-logout hover:bg-dark-700 transition-colors">
        <i class="ti ti-logout mr-3"></i>
        <span>Sign Out</span>
      </a>
    </div>
    <div class="w-full lg:ml-20 flex flex-col min-h-screen">
      <div
        class="hidden lg:fixed lg:flex top-0 right-0 left-20 items-center justify-end h-14 px-6 bg-dark-800 shadow-md z-10">
        <a href="https://github.com/yourusername/streamflow-lite" target="_blank"
          class="p-2 text-gray-400 hover:text-white transition-colors mr-2">
          <i class="ti ti-brand-github text-lg"></i>
        </a>
        <div class="relative">
          <button id="notification-btn" class="p-2 text-gray-400 hover:text-white transition-colors relative">
            <i class="ti ti-bell text-lg"></i>
            <span id="notification-badge" class="hidden absolute top-0.5 right-0.5 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>
          <div id="notification-dropdown"
            class="hidden absolute right-0 mt-1 w-80 bg-dark-800 border border-gray-700 rounded-lg shadow-xl z-50">
            <div class="p-3 border-b border-gray-700 flex items-center justify-between">
              <h3 class="font-medium">Notifications</h3>
              <% if (helpers.getUserRole && helpers.getUserRole(req) === 'admin') { %>
              <div class="flex items-center space-x-2">
                <button id="mark-all-read-btn" class="text-xs text-primary hover:underline">Mark all as read</button>
                <a href="/admin/notifications" class="text-xs text-blue-400 hover:underline">View all</a>
              </div>
              <% } else { %>
              <button id="mark-all-read-btn" class="text-xs text-primary hover:underline">Mark all as read</button>
              <% } %>
            </div>
            <div class="max-h-80 overflow-y-auto" id="notification-list">
              <div id="notification-loading" class="px-4 py-8 text-center text-gray-400">
                <i class="ti ti-loader-2 animate-spin text-lg mb-2"></i>
                <p class="text-sm">Loading notifications...</p>
              </div>
              <div id="notification-empty" class="px-4 py-8 text-center text-gray-400 hidden">
                <i class="ti ti-bell-off text-2xl mb-2"></i>
                <p class="text-sm">No notifications</p>
              </div>
            </div>
            <% if (helpers.getUserRole && helpers.getUserRole(req) === 'admin') { %>
            <a href="/admin/notifications" class="block text-center p-2 text-sm text-primary hover:underline border-t border-gray-700">
              View all notifications
            </a>
            <% } %>
          </div>
        </div>
      </div>
      <div class="p-6 pt-20 lg:pt-22 flex-1">
        <%- body %>
      </div>

      <!-- Footer -->
      <footer class="bg-dark-800 border-t border-gray-700 py-4 px-6 mt-auto">
        <div class="flex flex-col md:flex-row items-center justify-between gap-4">
          <div class="flex items-center gap-2">
            <div class="text-xs text-gray-500">
              StreamFlow <span class="text-xs font-medium bg-gray-700 px-1 rounded">v2.0</span>
            </div>
            <div class="h-3 w-px bg-gray-700"></div>
            <a href="https://youtube.com/@bangtutorial" target="_blank" class="text-xs text-gray-500 hover:underline">by
              Bang Tutorial</a>
          </div>
          <div class="flex items-center gap-2">
            <a href="https://donate.youtube101.id/" target="_blank" class="kirim-tip-button relative">
              <span class="relative z-10 flex items-center gap-1 text-white font-medium px-1.5 py-0.5 text-xs">
                <i class="ti ti-heart text-white"></i>
                <span>Donate</span>
              </span>
              <div
                class="kirim-tip-tooltip px-3 py-2 bg-white text-gray-800 text-xs rounded-lg shadow-lg whitespace-nowrap">
                Terima kasih sudah bantu support 🙏
                <div class="tooltip-arrow"></div>
              </div>
            </a>
          </div>
        </div>
      </footer>
    </div>
  </div>
  <div class="lg:hidden fixed bottom-0 left-0 right-0 bg-dark-800 border-t border-gray-700 shadow-lg z-30">
    <nav class="flex justify-around items-center h-16">
      <a href="/dashboard" class="bottom-nav-item <%= active === 'dashboard' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-broadcast"></i>
        <span>Streams</span>
      </a>
      <a href="/gallery" class="bottom-nav-item <%= active === 'gallery' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-video"></i>
        <span>Gallery</span>
      </a>
      <a href="/history" class="bottom-nav-item <%= active === 'history' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-history"></i>
        <span>History</span>
      </a>
      <a href="/settings" class="bottom-nav-item <%= active === 'settings' ? 'bottom-nav-active' : '' %>">
        <i class="ti ti-settings"></i>
        <span>Settings</span>
      </a>
      <button id="mobile-profile-btn" class="bottom-nav-item">
        <div class="relative">
          <div class="w-6 h-6 rounded-full overflow-hidden mx-auto">
            <%- helpers.getAvatar(req) %>
          </div>
        </div>
        <span>Profile</span>
      </button>
    </nav>
    <div id="mobile-profile-popup"
      class="fixed bottom-16 right-2 bg-dark-800 rounded-lg shadow-xl border border-gray-700 overflow-hidden hidden transform transition-all duration-200 ease-out opacity-0 translate-y-4 w-40">
      <div class="py-2 px-3">
        <div class="text-sm font-medium mb-1">
          <%= helpers.getUsername(req) %>
        </div>
        <div class="text-xs text-gray-400 mb-2">
          <%= req.session.email || '' %>
        </div>
        <div class="h-px bg-gray-700 my-2"></div>
        <a href="/logout" class="flex items-center py-2 text-red-400 hover:text-red-300 text-sm">
          <i class="ti ti-logout mr-2"></i>
          <span>Keluar</span>
        </a>
      </div>
    </div>
  </div>
  <div id="mobile-notification-popup"
    class="fixed bottom-16 right-2 bg-dark-800 rounded-lg shadow-xl border border-gray-700 overflow-hidden hidden transform transition-all duration-200 ease-out opacity-0 translate-y-4 w-72">
    <div class="p-3 border-b border-gray-700 flex items-center justify-between">
      <h3 class="font-medium">Notifications</h3>
      <button class="text-xs text-primary hover:underline">Mark all as read</button>
    </div>
    <div class="max-h-80 overflow-y-auto" id="mobile-notification-list">
      <div class="px-4 py-3 border-b border-gray-700 hover:bg-dark-700 transition-colors cursor-pointer" onclick="openStreamflowUpdateModal()">
        <div class="flex items-start">
          <div class="shrink-0 mr-3">
            <div class="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
              <i class="ti ti-info-circle text-primary"></i>
            </div>
          </div>
          <div>
            <p class="text-sm font-medium">StreamFlow v2 Released!</p>
            <p class="text-sm text-gray-300 mt-1">Fitur dan imporvisasi yang ada di StreamFlow v2.</p>
            <p class="text-xs text-gray-400 mt-2">Baru saja</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div id="streamflowUpdateModal" class="fixed inset-0 bg-black/60 z-[60] hidden items-center justify-center p-4 overflow-y-auto">
    <div class="bg-dark-800 rounded-xl shadow-xl w-full max-w-2xl m-auto flex flex-col max-h-[90vh] border border-gray-700/50">
      <div class="flex-shrink-0 flex items-center justify-between px-5 py-2 border-b border-gray-700/50">
        <h3 class="text-lg font-medium text-white flex items-center gap-2">
          <span class="text-base">🎉 Update StreamFlow v2</span>
        </h3>
        <button onclick="closeStreamflowUpdateModal()" class="text-gray-400 hover:text-white hover:bg-gray-700/50 px-2 py-1 rounded-lg transition-colors">
          <i class="ti ti-x text-lg"></i>
        </button>
      </div>
      <div class="p-5 overflow-y-auto text-gray-300 space-y-4">
        <p class="text-sm">Terima kasih buat teman-teman yang sudah menunggu. Berikut adalah update terbaru yang ada di StreamFlow v2:</p>

        <div class="space-y-4">
          <div class="flex items-start gap-3">
            <div class="shrink-0">
              <div class="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                <i class="ti ti-layout-dashboard text-blue-400"></i>
              </div>
            </div>
            <div>
              <h4 class="font-medium text-white text-sm">UI Baru yang Lebih Fresh</h4>
              <p class="text-gray-300 mt-1 text-sm">Tampilan baru yang lebih fresh dan user freindly. Sudah dibikin senyaman mungkin untuk kebutuhan live streaming.</p>
            </div>
          </div>

          <div class="flex items-start gap-3">
            <div class="shrink-0">
              <div class="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                <i class="ti ti-rocket text-green-400"></i>
              </div>
            </div>
            <div>
              <h4 class="font-medium text-white text-sm">Live Streaming Lebih Ringan</h4>
              <p class="text-gray-300 mt-1 text-sm">Di StreamFlow v2, proses streaming sudah dioptimalkan, sehingga jauh lebih ringan dibandingkan StreamFlow v1 sebelumnya.</p>
            </div>
          </div>

          <div class="flex items-start gap-3">
            <div class="shrink-0">
              <div class="w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center">
                <i class="ti ti-calendar text-purple-400"></i>
              </div>
            </div>
            <div>
              <h4 class="font-medium text-white text-sm">Penjadwalan Live Streaming</h4>
              <p class="text-gray-300 mt-1 text-sm">Di versi baru ini, fitur penjadwalan streaming ataupun auto stop sudah ditambahkan dan diperbaiki dibanding versi sebelumnya.</p>
            </div>
          </div>

          <div class="flex items-start gap-3">
            <div class="shrink-0">
              <div class="w-8 h-8 bg-amber-500/20 rounded-full flex items-center justify-center">
                <i class="ti ti-bug text-amber-400"></i>
              </div>
            </div>
            <div>
              <h4 class="font-medium text-white text-sm">Fix Bug</h4>
              <p class="text-gray-300 mt-1 text-sm">Beberapa bug yang sering ditemui di versi sebelumnya sudah di fix pada StreamFlow v2 ini, salah satunya bug streaming tiba-tiba terhenti.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
<style>
  #profile-dropdown {
    position: fixed;
    z-index: 100;
    min-width: 220px;
    border-radius: 8px;
    transition: all 0.2s ease;
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
    width: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    bottom: 80px;
    left: 15px;
  }
  #profile-dropdown.hidden {
    opacity: 0;
    transform: translateY(10px);
    pointer-events: none;
    display: block !important;
    visibility: hidden;
  }
  @media (max-width: 768px) {
    #profile-dropdown {
      min-width: 200px;
      right: 20px;
      left: auto;
      bottom: auto;
      top: 100px;
    }
  }
  .text-logout {
    color: #FF5555;
  }
  .text-logout:hover {
    color: #FF7777;
  }
  .kirim-tip-button {
    position: relative;
    border-radius: 5px;
    background: linear-gradient(45deg, #01935d, #01e304);
    transition: all 0.3s ease;
  }
  .kirim-tip-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 168, 107, 0.3);
  }
  .kirim-tip-tooltip {
    position: absolute;
    bottom: 100%;
    right: 0;
    margin-bottom: 10px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s, transform 0.3s;
    transform: translateY(5px);
    z-index: 50;
    filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));
  }
  .kirim-tip-button:hover .kirim-tip-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
  .tooltip-arrow {
    position: absolute;
    bottom: -6px;
    right: 10px;
    width: 12px;
    height: 12px;
    background-color: white;
    transform: rotate(45deg);
  }
  @media (max-width: 1023px) {
    body {
      padding-top: 16px;
    }
  }
  .bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    text-align: center;
    padding: 8px 0;
    color: #8F8F8F;
    position: relative;
    transition: color 0.2s ease;
  }
  .bottom-nav-item i {
    font-size: 22px;
    margin-bottom: 4px;
  }
  .bottom-nav-item span {
    font-size: 11px;
  }
  .bottom-nav-item:hover {
    color: #E5E5E5;
  }
  .bottom-nav-active {
    color: #0055FF;
  }
  .bottom-nav-active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 3px;
    background-color: #0055FF;
    border-radius: 0 0 4px 4px;
  }
  @media (max-width: 1023px) {
    .min-h-screen {
      padding-bottom: 80px !important;
    }
    #mobile-profile-popup.show {
      opacity: 1;
      transform: translateY(0);
      visibility: visible;
    }
  }
  #mobile-notification-popup.show {
    opacity: 1 !important;
    transform: translateY(0) !important;
    visibility: visible !important;
  }
  #mobile-notification-popup {
    z-index: 100;
    transition: opacity 0.3s, transform 0.3s, visibility 0.3s;
    display: block !important;
    visibility: hidden;
  }
  #mobile-notification-popup.hidden {
    display: none !important;
  }
</style>
<script>
  document.addEventListener('DOMContentLoaded', () => {
    const profileButton = document.getElementById('profile-menu-button');
    const profileDropdown = document.getElementById('profile-dropdown');
    if (!profileButton || !profileDropdown) return;
    profileButton.addEventListener('click', (e) => {
      e.stopPropagation();
      e.preventDefault();
      const isHidden = profileDropdown.classList.contains('hidden');
      profileDropdown.classList.toggle('hidden');
    });
    document.addEventListener('click', (e) => {
      const isClickInsideDropdown = profileDropdown.contains(e.target);
      const isClickOnButton = profileButton.contains(e.target);
      if (!isClickInsideDropdown && !isClickOnButton && !profileDropdown.classList.contains('hidden')) {
        profileDropdown.classList.add('hidden');
      }
    });
    profileDropdown.addEventListener('click', (e) => {
      e.stopPropagation();
    });
    const profileBtn = document.getElementById('mobile-profile-btn');
    const profilePopup = document.getElementById('mobile-profile-popup');
    if (profileBtn && profilePopup) {
      profileBtn.addEventListener('click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        if (profilePopup.classList.contains('hidden')) {
          profilePopup.classList.remove('hidden');
          setTimeout(() => {
            profilePopup.classList.add('show');
          }, 10);
        } else {
          profilePopup.classList.remove('show');
          setTimeout(() => {
            profilePopup.classList.add('hidden');
          }, 200);
        }
      });
      document.addEventListener('click', function (e) {
        if (!profilePopup.classList.contains('hidden') &&
          !profileBtn.contains(e.target) &&
          !profilePopup.contains(e.target)) {
          profilePopup.classList.remove('show');
          setTimeout(() => {
            profilePopup.classList.add('hidden');
          }, 200);
        }
      });
    }
    // Initialize notification system
    initializeNotificationSystem();

    const notificationBtn = document.getElementById('notification-btn');
    const notificationDropdown = document.getElementById('notification-dropdown');
    if (notificationBtn && notificationDropdown) {
      notificationBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        notificationDropdown.classList.toggle('hidden');
        if (!notificationDropdown.classList.contains('hidden')) {
          loadNotifications();
        }
      });
      document.addEventListener('click', (e) => {
        const isClickInsideDropdown = notificationDropdown.contains(e.target);
        const isClickOnButton = notificationBtn.contains(e.target);
        if (!isClickInsideDropdown && !isClickOnButton && !notificationDropdown.classList.contains('hidden')) {
          notificationDropdown.classList.add('hidden');
        }
      });
      notificationDropdown.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }
    const mobileNotificationBtn = document.getElementById('mobile-notification-btn');
    const mobileNotificationPopup = document.getElementById('mobile-notification-popup');
    if (mobileNotificationBtn && mobileNotificationPopup) {
      mobileNotificationBtn.addEventListener('click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        const btnRect = mobileNotificationBtn.getBoundingClientRect();
        mobileNotificationPopup.style.top = '64px';
        mobileNotificationPopup.style.right = '10px';
        mobileNotificationPopup.style.left = 'auto';
        mobileNotificationPopup.style.bottom = 'auto';
        if (mobileNotificationPopup.classList.contains('hidden')) {
          mobileNotificationPopup.classList.remove('hidden');
          setTimeout(() => {
            mobileNotificationPopup.classList.add('show');
          }, 10);
        } else {
          mobileNotificationPopup.classList.remove('show');
          setTimeout(() => {
            mobileNotificationPopup.classList.add('hidden');
          }, 200);
        }
      });
      document.addEventListener('click', function (e) {
        if (!mobileNotificationPopup.classList.contains('hidden') &&
          !mobileNotificationBtn.contains(e.target) &&
          !mobileNotificationPopup.contains(e.target)) {
          mobileNotificationPopup.classList.remove('show');
          setTimeout(() => {
            mobileNotificationPopup.classList.add('hidden');
          }, 200);
        }
      });
    }
  });

  function openStreamflowUpdateModal() {
    const modal = document.getElementById('streamflowUpdateModal');
    if (modal) {
      modal.classList.remove('hidden');
      modal.classList.add('flex');
      document.body.style.overflow = 'hidden';
    }
  }

  function closeStreamflowUpdateModal() {
    const modal = document.getElementById('streamflowUpdateModal');
    if (modal) {
      modal.classList.add('hidden');
      modal.classList.remove('flex');
      document.body.style.overflow = 'auto';
    }
  }

  // Notification system functions
  let notificationSocket = null;
  let isAdmin = <%= helpers.getUserRole && helpers.getUserRole(req) === 'admin' ? 'true' : 'false' %>;

  function initializeNotificationSystem() {
    if (typeof io !== 'undefined' && '<%= locals.session?.userId %>') {
      notificationSocket = io();

      if (isAdmin) {
        // Join admin notification room
        notificationSocket.emit('admin:join', {
          isAdmin: true,
          userId: '<%= locals.session?.userId %>'
        });
      } else {
        // Join user notification room
        notificationSocket.emit('user:join', {
          userId: '<%= locals.session?.userId %>'
        });
      }

      // Listen for real-time notifications
      notificationSocket.on('notification:new', (notification) => {
        addNotificationToDropdown(notification);
        updateNotificationBadge();
        showNotificationToast(notification);
      });

      notificationSocket.on('notification:updated', (data) => {
        updateNotificationInDropdown(data.id, { isRead: data.isRead });
        updateNotificationBadge();
      });

      notificationSocket.on('notification:allMarkedRead', () => {
        markAllNotificationsAsReadInDropdown();
        updateNotificationBadge();
      });
    }

    // Mark all as read button
    const markAllReadBtn = document.getElementById('mark-all-read-btn');
    if (markAllReadBtn) {
      markAllReadBtn.addEventListener('click', markAllNotificationsAsRead);
    }
  }

  async function loadNotifications() {
    const loadingEl = document.getElementById('notification-loading');
    const emptyEl = document.getElementById('notification-empty');

    try {
      loadingEl.classList.remove('hidden');
      emptyEl.classList.add('hidden');

      const endpoint = isAdmin ? '/api/notifications/admin?limit=10' : '/api/notifications/user?limit=10';
      const response = await fetch(endpoint);
      const data = await response.json();

      if (data.success) {
        renderNotificationsInDropdown(data.notifications);
        updateNotificationBadge(data.unreadCount);
      } else {
        console.error('Failed to load notifications:', data.error);
      }
    } catch (error) {
      console.error('Error loading notifications:', error);
    } finally {
      loadingEl.classList.add('hidden');
    }
  }

  function renderNotificationsInDropdown(notifications) {
    const listEl = document.getElementById('notification-list');
    const emptyEl = document.getElementById('notification-empty');

    // Clear existing notifications (except loading and empty states)
    const existingNotifications = listEl.querySelectorAll('.notification-item');
    existingNotifications.forEach(item => item.remove());

    if (notifications.length === 0) {
      emptyEl.classList.remove('hidden');
      return;
    }

    emptyEl.classList.add('hidden');

    notifications.forEach(notification => {
      const notificationEl = createNotificationElement(notification);
      listEl.appendChild(notificationEl);
    });
  }

  function createNotificationElement(notification) {
    const div = document.createElement('div');
    div.className = `notification-item px-4 py-3 border-b border-gray-700 hover:bg-dark-700 transition-colors cursor-pointer ${notification.is_read ? 'opacity-60' : ''}`;
    div.dataset.id = notification.id;

    const typeIcons = {
      info: 'ti-info-circle',
      warning: 'ti-alert-triangle',
      error: 'ti-alert-circle',
      success: 'ti-check-circle'
    };

    const typeColors = {
      info: 'text-blue-400 bg-blue-500/20',
      warning: 'text-yellow-400 bg-yellow-500/20',
      error: 'text-red-400 bg-red-500/20',
      success: 'text-green-400 bg-green-500/20'
    };

    const priorityColors = {
      low: 'bg-gray-500',
      normal: 'bg-blue-500',
      high: 'bg-orange-500',
      critical: 'bg-red-500'
    };

    div.innerHTML = `
      <div class="flex items-start">
        <div class="shrink-0 mr-3">
          <div class="w-8 h-8 ${typeColors[notification.type]} rounded-full flex items-center justify-center">
            <i class="ti ${typeIcons[notification.type]} ${typeColors[notification.type].split(' ')[0]}"></i>
          </div>
        </div>
        <div class="flex-1">
          <div class="flex items-center space-x-2 mb-1">
            <p class="text-sm font-medium">${notification.title}</p>
            ${notification.priority !== 'normal' ? `<span class="px-1.5 py-0.5 text-xs rounded-full ${priorityColors[notification.priority]} text-white">${notification.priority.toUpperCase()}</span>` : ''}
            ${!notification.is_read ? '<span class="w-2 h-2 bg-blue-500 rounded-full"></span>' : ''}
          </div>
          <p class="text-sm text-gray-300 mb-1">${notification.message}</p>
          <p class="text-xs text-gray-400">${new Date(notification.created_at).toLocaleString()}</p>
        </div>
      </div>
    `;

    // Add click handler to mark as read
    div.addEventListener('click', () => {
      if (!notification.is_read) {
        markNotificationAsRead(notification.id);
      }
    });

    return div;
  }

  document.addEventListener('keydown', (e) => {
    const updateModal = document.getElementById('streamflowUpdateModal');
    if (e.key === 'Escape' && updateModal && !updateModal.classList.contains('hidden')) {
      closeStreamflowUpdateModal();
    }
  });

  document.addEventListener('click', (e) => {
    const updateModal = document.getElementById('streamflowUpdateModal');
    if (updateModal && e.target === updateModal) {
      closeStreamflowUpdateModal();
    }
  });

  function addNotificationToDropdown(notification) {
    const listEl = document.getElementById('notification-list');
    const emptyEl = document.getElementById('notification-empty');

    emptyEl.classList.add('hidden');

    const notificationEl = createNotificationElement(notification);
    listEl.insertBefore(notificationEl, listEl.firstChild);

    // Remove oldest notification if we have more than 10
    const notifications = listEl.querySelectorAll('.notification-item');
    if (notifications.length > 10) {
      notifications[notifications.length - 1].remove();
    }
  }

  function updateNotificationInDropdown(id, updates) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item && updates.isRead) {
      item.classList.add('opacity-60');
      const unreadDot = item.querySelector('.w-2.h-2.bg-blue-500');
      if (unreadDot) unreadDot.remove();
    }
  }

  function markAllNotificationsAsReadInDropdown() {
    const items = document.querySelectorAll('.notification-item');
    items.forEach(item => {
      item.classList.add('opacity-60');
      const unreadDot = item.querySelector('.w-2.h-2.bg-blue-500');
      if (unreadDot) unreadDot.remove();
    });
  }

  async function markNotificationAsRead(id) {
    try {
      const endpoint = isAdmin ? `/api/notifications/admin/${id}/mark-read` : `/api/notifications/user/${id}/mark-read`;
      const response = await fetch(endpoint, {
        method: 'POST'
      });
      const data = await response.json();

      if (data.success) {
        updateNotificationInDropdown(id, { isRead: true });
        updateNotificationBadge();
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  async function markAllNotificationsAsRead() {
    try {
      const endpoint = isAdmin ? '/api/notifications/admin/mark-all-read' : '/api/notifications/user/mark-all-read';
      const response = await fetch(endpoint, {
        method: 'POST'
      });
      const data = await response.json();

      if (data.success) {
        markAllNotificationsAsReadInDropdown();
        updateNotificationBadge(0);
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }

  function updateNotificationBadge(count = null) {
    const badge = document.getElementById('notification-badge');
    if (!badge) return;

    if (count !== null) {
      if (count > 0) {
        badge.classList.remove('hidden');
      } else {
        badge.classList.add('hidden');
      }
    } else {
      // Count unread notifications in dropdown
      const unreadItems = document.querySelectorAll('.notification-item:not(.opacity-60)');
      if (unreadItems.length > 0) {
        badge.classList.remove('hidden');
      } else {
        badge.classList.add('hidden');
      }
    }
  }

  function showNotificationToast(notification) {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 max-w-sm bg-dark-800 border border-gray-700 rounded-lg shadow-xl p-4 transform transition-all duration-300 translate-x-full`;

    const typeColors = {
      info: 'border-blue-500',
      warning: 'border-yellow-500',
      error: 'border-red-500',
      success: 'border-green-500'
    };

    toast.classList.add(typeColors[notification.type] || 'border-blue-500');

    toast.innerHTML = `
      <div class="flex items-start">
        <div class="flex-1">
          <h4 class="font-medium text-white text-sm">${notification.title}</h4>
          <p class="text-gray-300 text-sm mt-1">${notification.message}</p>
        </div>
        <button class="ml-2 text-gray-400 hover:text-white" onclick="this.parentElement.parentElement.remove()">
          <i class="ti ti-x"></i>
        </button>
      </div>
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
      toast.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
      toast.classList.add('translate-x-full');
      setTimeout(() => {
        if (toast.parentElement) {
          toast.remove();
        }
      }, 300);
    }, 5000);
  }
</script>