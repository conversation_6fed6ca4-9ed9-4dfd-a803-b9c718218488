const { db } = require('./db/database');
const notificationService = require('./services/notificationService');
const Notification = require('./models/Notification');

async function testNotifications() {
  console.log('🧪 Testing notification system...');

  try {
    // Test 1: Create admin notification
    console.log('\n1. Testing admin notification...');
    const adminNotification = await notificationService.notifySystemAlert(
      'Test Admin Notification',
      'This is a test admin notification',
      'high'
    );
    console.log('✅ Admin notification created:', adminNotification.id);

    // Test 2: Create user notification for regular user
    console.log('\n2. Testing user notification...');
    const testUserId = '8667e873-0470-4e45-b2f5-0fafcf27e1fb'; // Regular user from server logs
    const userNotification = await notificationService.notifyUserSpecific(
      testUserId,
      'Test User Notification',
      'This is a test notification for you!',
      'success',
      'high'
    );
    console.log('✅ User notification created:', userNotification.id);

    // Test 3: Create another user notification for admin user
    console.log('\n3. Testing user notification for admin...');
    const adminUserId = '28fd3c08-af4b-4fe8-9b36-12e0089bd409'; // Admin user from server logs
    const adminUserNotification = await notificationService.notifyUserSpecific(
      adminUserId,
      'Test Admin User Notification',
      'This is a test notification for the admin user!',
      'info',
      'normal'
    );
    console.log('✅ Admin user notification created:', adminUserNotification.id);

    // Test 4: Get admin notifications
    console.log('\n4. Testing admin notification retrieval...');
    const adminNotifications = await Notification.findAll({ target_user_id: null, limit: 5 });
    console.log(`✅ Found ${adminNotifications.length} admin notifications`);

    // Test 5: Get user notifications for regular user
    console.log('\n5. Testing user notification retrieval...');
    const userNotifications = await Notification.findAll({ target_user_id: testUserId, limit: 5 });
    console.log(`✅ Found ${userNotifications.length} user notifications for regular user`);

    // Test 6: Get user notifications for admin user
    console.log('\n6. Testing admin user notification retrieval...');
    const adminUserNotifications = await Notification.findAll({ target_user_id: adminUserId, limit: 5 });
    console.log(`✅ Found ${adminUserNotifications.length} user notifications for admin user`);

    // Test 7: Get unread counts
    console.log('\n7. Testing unread counts...');
    const adminUnreadCount = await Notification.getUnreadCount(null);
    const userUnreadCount = await Notification.getUnreadCount(testUserId);
    const adminUserUnreadCount = await Notification.getUnreadCount(adminUserId);
    console.log(`✅ Admin unread count: ${adminUnreadCount}`);
    console.log(`✅ Regular user unread count: ${userUnreadCount}`);
    console.log(`✅ Admin user unread count: ${adminUserUnreadCount}`);

    console.log('\n🎉 All notification tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Close database connection
    if (db) {
      db.close();
    }
    process.exit(0);
  }
}

// Run the test
testNotifications();
