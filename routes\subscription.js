const express = require('express');
const router = express.Router();
const Subscription = require('../models/Subscription');
const User = require('../models/User');
const QuotaMiddleware = require('../middleware/quotaMiddleware');
const notificationService = require('../services/notificationService');

// Middleware to check authentication
const isAuthenticated = (req, res, next) => {
  if (req.session.userId) {
    return next();
  }
  res.redirect('/login');
};

// Get all subscription plans
router.get('/plans', async (req, res) => {
  try {
    const plans = await Subscription.getAllPlans();

    // If user is logged in, get their current subscription
    let currentSubscription = null;
    let quotaInfo = null;

    if (req.session.userId) {
      currentSubscription = await Subscription.getUserSubscription(req.session.userId);
      quotaInfo = await QuotaMiddleware.getUserQuotaInfo(req.session.userId);
    }

    res.render('subscription/plans', {
      title: 'Subscription Plans',
      active: 'subscription',
      plans,
      currentSubscription,
      quotaInfo
    });
  } catch (error) {
    console.error('Get plans error:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load subscription plans',
      error: error
    });
  }
});

// Get user's current subscription info
router.get('/current', isAuthenticated, async (req, res) => {
  try {
    const subscription = await Subscription.getUserSubscription(req.session.userId);
    const quotaInfo = await QuotaMiddleware.getUserQuotaInfo(req.session.userId);

    res.json({
      subscription,
      quota: quotaInfo
    });
  } catch (error) {
    console.error('Get current subscription error:', error);
    res.status(500).json({ error: 'Failed to get subscription information' });
  }
});

// Subscribe to a plan
router.post('/subscribe', isAuthenticated, async (req, res) => {
  try {
    const { planId, paymentMethod = 'manual' } = req.body;

    if (!planId) {
      return res.status(400).json({ error: 'Plan ID is required' });
    }

    const plan = await Subscription.getPlanById(planId);
    if (!plan) {
      return res.status(404).json({ error: 'Plan not found' });
    }

    // Get user's current plan
    const user = await User.findById(req.session.userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if user already has an active subscription (excluding Preview plan)
    const existingSubscription = await Subscription.getUserSubscription(req.session.userId);
    if (existingSubscription) {
      return res.status(400).json({
        error: 'You already have an active subscription',
        message: 'Please cancel your current subscription before subscribing to a new plan'
      });
    }

    // Allow upgrade from Preview plan to any other plan
    if (user.plan_type === 'Preview' && plan.name === 'Preview') {
      return res.status(400).json({
        error: 'You are already on the Preview plan',
        message: 'You are already using the Preview plan'
      });
    }

    // Calculate end date (30 days from now for monthly plans)
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 30);

    // Create subscription
    const subscription = await Subscription.createSubscription({
      user_id: req.session.userId,
      plan_id: planId,
      status: 'active',
      end_date: endDate.toISOString(),
      payment_method: paymentMethod
    });

    // Update user's plan limits
    await User.updatePlan(
      req.session.userId,
      plan.name,
      plan.max_streaming_slots,
      plan.max_storage_gb
    );

    console.log(`📈 User ${user.username} upgraded from ${user.plan_type} to ${plan.name} plan`);

    // Send notification for plan upgrade
    try {
      await notificationService.notifyUserUpgraded(req.session.userId, user.username, user.plan_type, plan.name);
    } catch (notifError) {
      console.error('Error sending plan upgrade notification:', notifError);
    }

    res.json({
      success: true,
      message: 'Successfully subscribed to plan',
      subscription,
      plan
    });
  } catch (error) {
    console.error('Subscribe error:', error);
    res.status(500).json({ error: 'Failed to create subscription' });
  }
});

// Cancel subscription
router.post('/cancel', isAuthenticated, async (req, res) => {
  try {
    // Get user's current plan
    const user = await User.findById(req.session.userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Prevent Preview plan users from cancelling (it's the default plan)
    if (user.plan_type === 'Preview') {
      return res.status(400).json({
        error: 'Cannot cancel Preview plan',
        message: 'Preview plan is the default plan and cannot be cancelled'
      });
    }

    const subscription = await Subscription.getUserSubscription(req.session.userId);

    if (!subscription) {
      return res.status(404).json({ error: 'No active subscription found' });
    }

    // Update subscription status to cancelled
    await Subscription.updateSubscriptionStatus(subscription.id, 'cancelled');

    // Get current stream count before deletion
    const { db } = require('../db/database');
    const streamCount = await new Promise((resolve, reject) => {
      db.get(
        "SELECT COUNT(*) as count FROM streams WHERE user_id = ?",
        [req.session.userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row.count);
        }
      );
    });

    // Delete all user streams since Preview plan has 0 slots
    console.log(`🗑️ Deleting all streams for user ${user.username} (Preview plan has 0 slots)`);
    const deletionResult = await Subscription.deleteAllUserStreamsForPreviewPlan(req.session.userId);
    console.log(`✅ Stream deletion completed: ${deletionResult.deleted} streams deleted`);

    // Downgrade user to Preview plan (the default plan)
    const previewPlan = await Subscription.getPlanByName('Preview');
    if (previewPlan) {
      await User.updatePlan(
        req.session.userId,
        'Preview',
        previewPlan.max_streaming_slots,
        previewPlan.max_storage_gb
      );
      console.log(`📉 User ${user.username} downgraded to Preview plan after cancellation`);
    } else {
      // Fallback to hardcoded Preview plan values if plan not found
      await User.updatePlan(
        req.session.userId,
        'Preview',
        0, // Preview plan: 0 slots
        1 // Preview plan: 1GB storage
      );
      console.log(`📉 User ${user.username} downgraded to Preview plan (fallback values) after cancellation`);
    }

    let message = 'Subscription cancelled successfully. You have been downgraded to the Preview plan.';

    // Inform user about deleted streams
    if (deletionResult.deleted > 0) {
      message += ` All ${deletionResult.deleted} of your streams have been automatically deleted since Preview plan allows 0 streaming slots.`;
    }

    // Send notification for plan downgrade
    try {
      await notificationService.notifyUserDowngraded(req.session.userId, user.username, user.plan_type, 'Preview');
    } catch (notifError) {
      console.error('Error sending plan downgrade notification:', notifError);
    }

    res.json({
      success: true,
      message,
      originalStreamCount: streamCount,
      deletedStreams: deletionResult.deleted,
      deletedStreamDetails: deletionResult.streams,
      newPlan: 'Preview'
    });
  } catch (error) {
    console.error('Cancel subscription error:', error);
    res.status(500).json({ error: 'Failed to cancel subscription' });
  }
});

// Upgrade/Downgrade subscription
router.post('/change', isAuthenticated, async (req, res) => {
  try {
    const { planId } = req.body;

    if (!planId) {
      return res.status(400).json({ error: 'Plan ID is required' });
    }

    const newPlan = await Subscription.getPlanById(planId);
    if (!newPlan) {
      return res.status(404).json({ error: 'Plan not found' });
    }

    const currentSubscription = await Subscription.getUserSubscription(req.session.userId);

    if (currentSubscription) {
      // Cancel current subscription
      await Subscription.updateSubscriptionStatus(currentSubscription.id, 'cancelled');
    }

    // Create new subscription
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 30);

    const newSubscription = await Subscription.createSubscription({
      user_id: req.session.userId,
      plan_id: planId,
      status: 'active',
      end_date: endDate.toISOString(),
      payment_method: 'manual'
    });

    // Update user's plan limits
    await User.updatePlan(
      req.session.userId,
      newPlan.name,
      newPlan.max_streaming_slots,
      newPlan.max_storage_gb
    );

    res.json({
      success: true,
      message: 'Plan changed successfully',
      subscription: newSubscription,
      plan: newPlan
    });
  } catch (error) {
    console.error('Change plan error:', error);
    res.status(500).json({ error: 'Failed to change subscription plan' });
  }
});

// Get subscription history
router.get('/history', isAuthenticated, async (req, res) => {
  try {
    const { db } = require('../db/database');

    const history = await new Promise((resolve, reject) => {
      db.all(`
        SELECT us.*, sp.name as plan_name, sp.price, sp.currency
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = ?
        ORDER BY us.created_at DESC
      `, [req.session.userId], (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });

    res.json(history);
  } catch (error) {
    console.error('Get subscription history error:', error);
    res.status(500).json({ error: 'Failed to get subscription history' });
  }
});

// Check quota status
router.get('/quota', isAuthenticated, async (req, res) => {
  try {
    const quotaInfo = await QuotaMiddleware.getUserQuotaInfo(req.session.userId);
    res.json(quotaInfo);
  } catch (error) {
    console.error('Get quota error:', error);
    res.status(500).json({ error: 'Failed to get quota information' });
  }
});

module.exports = router;
